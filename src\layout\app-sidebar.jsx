import * as React from "react";
import { <PERSON> } from "@tanstack/react-router";
import {
  LayoutDashboard,
  ShieldAlert,
  Server,
  Logs,
  ChartNetwork,
} from "lucide-react";

import { NavMain } from "@/layout/nav-main";
import { NavFeature } from "@/layout/nav-feature";
import { NavUser } from "@/layout/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar";
import { useTheme } from "@/components/theme-provider";

// Logo imports
import collapsedLogo from "@/assets/images/collapsed-logo.svg";
import lightmodeLogo from "@/assets/images/lightmode-logo.svg";
import darkmodeLogo from "@/assets/images/darkmode-logo.svg";

// Custom hook to get the resolved theme (system -> actual theme)
function useResolvedTheme() {
  const { theme } = useTheme();
  const [resolvedTheme, setResolvedTheme] = React.useState("light");

  React.useEffect(() => {
    if (theme === "system") {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
        .matches
        ? "dark"
        : "light";
      setResolvedTheme(systemTheme);

      // Listen for system theme changes
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = (e) => {
        setResolvedTheme(e.matches ? "dark" : "light");
      };

      mediaQuery.addEventListener("change", handleChange);
      return () => mediaQuery.removeEventListener("change", handleChange);
    } else {
      setResolvedTheme(theme);
    }
  }, [theme]);

  return resolvedTheme;
}

// This is sample data.
const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Devices",
      url: "/devices",
      icon: Server,
    },
    {
      title: "Logs",
      url: "/logs",
      icon: Logs,
    },
    {
      title: "Topology",
      url: "/topology",
      icon: ChartNetwork,
    },
  ],
  navFeature: [
    {
      title: "Idps",
      url: "/idps",
      icon: ShieldAlert,
    },
  ],
};

export function AppSidebar({ ...props }) {
  const { state } = useSidebar();
  const resolvedTheme = useResolvedTheme();

  // Determine which logo to use
  const getLogoSrc = () => {
    if (state === "collapsed") {
      return collapsedLogo;
    }
    return resolvedTheme === "dark" ? darkmodeLogo : lightmodeLogo;
  };

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link to="/">
                <img
                  src={getLogoSrc()}
                  alt="Logo"
                  className={state === "collapsed" ? "!size-5" : "h-6 w-auto"}
                />
                <span className="text-base font-semibold">Acme Inc.</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavFeature items={data.navFeature} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
