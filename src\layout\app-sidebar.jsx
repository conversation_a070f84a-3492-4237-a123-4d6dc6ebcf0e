import * as React from "react";
import { <PERSON> } from "@tanstack/react-router";
import {
  LayoutDashboard,
  ShieldAlert,
  Server,
  Logs,
  ChartNetwork,
  GalleryVerticalEnd,
} from "lucide-react";

import { NavMain } from "@/layout/nav-main";
import { NavFeature } from "@/layout/nav-feature";
import { NavUser } from "@/layout/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";

// This is sample data.
const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "Devices",
      url: "/devices",
      icon: Server,
    },
    {
      title: "Logs",
      url: "/logs",
      icon: Logs,
    },
    {
      title: "Topology",
      url: "/topology",
      icon: ChartNetwork,
    },
  ],
  navFeature: [
    {
      title: "Idps",
      url: "/idps",
      icon: ShieldAlert,
    },
  ],
};

export function AppSidebar({ ...props }) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link to="/">
                <GalleryVerticalEnd className="!size-5" />
                <span className="text-base font-semibold">Acme Inc.</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavFeature items={data.navFeature} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
